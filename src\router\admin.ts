import type { RouteRecordRaw } from 'vue-router'

export default [
  {
    path: '/admin/menu',
    name: 'menu',
    component: () => import('~/pages/menu/index.vue'),
    meta: {},
  },
  {
    path: '/admin/dict',
    name: 'dict',
    component: () => import('~/pages/dict/index.vue'),
    meta: {},
  },
  {
    path: '/admin/user',
    name: 'user',
    component: () => import('~/pages/user/index.vue'),
    meta: {
      access: 'ADMIN',
    },
  },
  {
    path: '/user/changePassword',
    name: 'changePassword',
    component: () => import('~/pages/user/password/change.vue'),
    meta: {
    },
  },
  {
    path: '/theoretical',
    name: 'theoretical',
    component: () => import('~/pages/theoretical/index.vue'),
    meta: {},
  },
  {
    path: '/event',
    name: 'event',
    component: () => import('~/pages/event/index.vue'),
    meta: {},
  },
  {
    path: '/target-oee',
    name: 'target-oee',
    component: () => import('~/pages/target-oee/index.vue'),
    meta: {},
  },
  {
    path: '/abnormal',
    name: 'abnormal',
    component: () => import('~/pages/abnormal/index.vue'),
    meta: {},
  },
  {
    path: '/change',
    name: 'change',
    component: () => import('~/pages/change/index.vue'),
    meta: {},
  },
  {
    path: '/product',
    name: 'product',
    component: () => import('~/pages/product/index.vue'),
    meta: {},
  },
  {
    path: '/device',
    name: 'device',
    component: () => import('~/pages/device/index.vue'),
    meta: {},
  },
  {
    path: '/export/daily',
    name: 'export-daily',
    component: () => import('~/pages/export/daily/index.vue'),
    meta: {},
  },
] satisfies RouteRecordRaw[]
